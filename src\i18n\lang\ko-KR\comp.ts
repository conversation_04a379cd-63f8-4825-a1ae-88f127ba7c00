export default {
  installApp: {
    description: '앱을 설치하여 더 나은 경험을 얻으세요',
    noPrompt: '다시 묻지 않기',
    install: '지금 설치',
    cancel: '나중에 설치',
    download: '다운로드',
    downloadFailed: '다운로드 실패',
    downloadComplete: '다운로드 완료',
    downloadProblem: '다운로드에 문제가 있나요?',
    downloadProblemLinkText: '최신 버전 다운로드'
  },
  playlistDrawer: {
    title: '플레이리스트에 추가',
    createPlaylist: '새 플레이리스트 만들기',
    cancelCreate: '만들기 취소',
    create: '만들기',
    playlistName: '플레이리스트 이름',
    privatePlaylist: '비공개 플레이리스트',
    publicPlaylist: '공개 플레이리스트',
    createSuccess: '플레이리스트 생성 성공',
    createFailed: '플레이리스트 생성 실패',
    addSuccess: '곡 추가 성공',
    addFailed: '곡 추가 실패',
    private: '비공개',
    public: '공개',
    count: '곡',
    loginFirst: '먼저 로그인해주세요',
    getPlaylistFailed: '플레이리스트 가져오기 실패',
    inputPlaylistName: '플레이리스트 이름을 입력해주세요'
  },
  update: {
    title: '새 버전 발견',
    currentVersion: '현재 버전',
    cancel: '나중에 업데이트',
    prepareDownload: '다운로드 준비 중...',
    downloading: '다운로드 중...',
    nowUpdate: '지금 업데이트',
    downloadFailed: '다운로드 실패, 다시 시도하거나 수동으로 다운로드해주세요',
    startFailed: '다운로드 시작 실패, 다시 시도하거나 수동으로 다운로드해주세요',
    noDownloadUrl: '현재 시스템에 적합한 설치 패키지를 찾을 수 없습니다. 수동으로 다운로드해주세요',
    installConfirmTitle: '업데이트 설치',
    installConfirmContent: '앱을 닫고 업데이트를 설치하시겠습니까?',
    manualInstallTip: '앱을 닫은 후 설치 프로그램이 정상적으로 나타나지 않으면 다운로드 폴더에서 파일을 찾아 수동으로 열어주세요.',
    yesInstall: '지금 설치',
    noThanks: '나중에 설치',
    fileLocation: '파일 위치',
    copy: '경로 복사',
    copySuccess: '경로가 클립보드에 복사됨',
    copyFailed: '복사 실패',
    backgroundDownload: '백그라운드 다운로드'
  },
  coffee: {
    title: '커피 한 잔 사주세요',
    alipay: '알리페이',
    wechat: '위챗 페이',
    alipayQR: '알리페이 결제 QR코드',
    wechatQR: '위챗 결제 QR코드',
    coffeeDesc: '커피 한 잔, 하나의 지원',
    coffeeDescLinkText: '더 보기',
    qqGroup: 'QQ 채널: algermusic',
    messages: {
      copySuccess: '클립보드에 복사됨'
    },
    donateList: '커피 한 잔 사주세요'
  },
  playlistType: {
    title: '플레이리스트 분류',
    showAll: '모두 표시',
    hide: '일부 숨기기'
  },
  recommendAlbum: {
    title: '최신 앨범'
  },
  recommendSinger: {
    title: '일일 추천',
    songlist: '일일 추천 목록'
  },
  recommendSonglist: {
    title: '이번 주 인기 음악'
  },
  searchBar: {
    login: '로그인',
    toLogin: '로그인하기',
    logout: '로그아웃',
    set: '설정',
    theme: '테마',
    restart: '재시작',
    refresh: '새로고침',
    currentVersion: '현재 버전',
    searchPlaceholder: '검색해보세요...',
    zoom: '페이지 확대/축소',
    zoom100: '표준 확대/축소 100%',
    resetZoom: '클릭하여 확대/축소 재설정',
    zoomDefault: '표준 확대/축소'
  },
  titleBar: {
    closeTitle: '닫기 방법을 선택해주세요',
    minimizeToTray: '트레이로 최소화',
    exitApp: '앱 종료',
    rememberChoice: '선택 기억하기',
    closeApp: '앱 닫기'
  },
  userPlayList: {
    title: '{name}의 자주 듣는 음악'
  },
  musicList: {
    searchSongs: '곡 검색',
    noSearchResults: '관련 곡을 찾을 수 없습니다',
    switchToNormal: '기본 레이아웃으로 전환',
    switchToCompact: '컴팩트 레이아웃으로 전환',
    playAll: '모두 재생',
    collect: '수집',
    collectSuccess: '수집 성공',
    cancelCollectSuccess: '수집 취소 성공',
    operationFailed: '작업 실패',
    cancelCollect: '수집 취소',
    addToPlaylist: '재생 목록에 추가',
    addToPlaylistSuccess: '재생 목록에 추가 성공',
    songsAlreadyInPlaylist: '곡이 이미 재생 목록에 있습니다'
  },
  playlist: {
    import: {
      button: '플레이리스트 가져오기',
      title: '플레이리스트 가져오기',
      description: '메타데이터/텍스트/링크 세 가지 방법으로 플레이리스트 가져오기 지원',
      linkTab: '링크 가져오기',
      textTab: '텍스트 가져오기',
      localTab: '메타데이터 가져오기',
      linkPlaceholder: '플레이리스트 링크를 입력하세요. 한 줄에 하나씩',
      textPlaceholder: '곡 정보를 입력하세요. 형식: 곡명 가수명',
      localPlaceholder: 'JSON 형식의 곡 메타데이터를 입력하세요',
      linkTips: '지원되는 링크 소스:',
      linkTip1: '플레이리스트를 위챗/웨이보/QQ로 공유한 후 링크 복사',
      linkTip2: '플레이리스트/개인 홈페이지 링크 직접 복사',
      linkTip3: '기사 링크 직접 복사',
      textTips: '곡 정보를 입력하세요. 한 줄에 한 곡씩',
      textFormat: '형식: 곡명 가수명',
      localTips: '곡 메타데이터를 추가해주세요',
      localFormat: '형식 예시:',
      songNamePlaceholder: '곡명',
      artistNamePlaceholder: '아티스트명',
      albumNamePlaceholder: '앨범명',
      addSongButton: '곡 추가',
      addLinkButton: '링크 추가',
      importToStarPlaylist: '내가 좋아하는 음악으로 가져오기',
      playlistNamePlaceholder: '플레이리스트 이름을 입력하세요',
      importButton: '가져오기 시작',
      emptyLinkWarning: '플레이리스트 링크를 입력해주세요',
      emptyTextWarning: '곡 정보를 입력해주세요',
      emptyLocalWarning: '곡 메타데이터를 입력해주세요',
      invalidJsonFormat: 'JSON 형식이 올바르지 않습니다',
      importSuccess: '가져오기 작업 생성 성공',
      importFailed: '가져오기 실패',
      importStatus: '가져오기 상태',
      refresh: '새로고침',
      taskId: '작업 ID',
      status: '상태',
      successCount: '성공 수',
      failReason: '실패 이유',
      unknownError: '알 수 없는 오류',
      statusPending: '처리 대기 중',
      statusProcessing: '처리 중',
      statusSuccess: '가져오기 성공',
      statusFailed: '가져오기 실패',
      statusUnknown: '알 수 없는 상태',
      taskList: '작업 목록',
      taskListTitle: '가져오기 작업 목록',
      action: '작업',
      select: '선택',
      fetchTaskListFailed: '작업 목록 가져오기 실패',
      noTasks: '가져오기 작업이 없습니다',
      clearTasks: '작업 지우기',
      clearTasksConfirmTitle: '지우기 확인',
      clearTasksConfirmContent: '모든 가져오기 작업 기록을 지우시겠습니까? 이 작업은 되돌릴 수 없습니다.',
      confirm: '확인',
      cancel: '취소',
      clearTasksSuccess: '작업 목록이 지워졌습니다',
      clearTasksFailed: '작업 목록 지우기 실패'
    }
  },
  settings: '설정',
  user: '사용자',
  toplist: '순위',
  history: '수집 기록',
  list: '플레이리스트',
  mv: 'MV',
  home: '홈',
  search: '검색'
};