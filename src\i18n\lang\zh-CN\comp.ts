export default {
  installApp: {
    description: '安装应用程序，获得更好的体验',
    noPrompt: '不再提示',
    install: '立即安装',
    cancel: '暂不安装',
    download: '下载',
    downloadFailed: '下载失败',
    downloadComplete: '下载完成',
    downloadProblem: '下载遇到问题？去',
    downloadProblemLinkText: '下载最新版本'
  },
  playlistDrawer: {
    title: '添加到歌单',
    createPlaylist: '创建新歌单',
    cancelCreate: '取消创建',
    create: '创建',
    playlistName: '歌单名称',
    privatePlaylist: '私密歌单',
    publicPlaylist: '公开歌单',
    createSuccess: '歌单创建成功',
    createFailed: '歌单创建失败',
    addSuccess: '歌曲添加成功',
    addFailed: '歌曲添加失败',
    private: '私密',
    public: '公开',
    count: '首歌曲',
    loginFirst: '请先登录',
    getPlaylistFailed: '获取歌单失败',
    inputPlaylistName: '请输入歌单名称'
  },
  update: {
    title: '发现新版本',
    currentVersion: '当前版本',
    cancel: '暂不更新',
    prepareDownload: '准备下载...',
    downloading: '下载中...',
    nowUpdate: '立即更新',
    downloadFailed: '下载失败，请重试或手动下载',
    startFailed: '启动下载失败，请重试或手动下载',
    noDownloadUrl: '未找到适合当前系统的安装包，请手动下载',
    installConfirmTitle: '安装更新',
    installConfirmContent: '是否关闭应用并安装更新？',
    manualInstallTip: '如果关闭应用后没有正常弹出安装程序，请至下载文件夹查找文件并手动打开。',
    yesInstall: '立即安装',
    noThanks: '稍后安装',
    fileLocation: '文件位置',
    copy: '复制路径',
    copySuccess: '路径已复制到剪贴板',
    copyFailed: '复制失败',
    backgroundDownload: '后台下载'
  },
  coffee: {
    title: '请我喝咖啡',
    alipay: '支付宝',
    wechat: '微信支付',
    alipayQR: '支付宝收款码',
    wechatQR: '微信收款码',
    coffeeDesc: '一杯咖啡，一份支持',
    coffeeDescLinkText: '查看更多',
    qqGroup: 'QQ频道：algermusic',
    messages: {
      copySuccess: '已复制到剪贴板'
    },
    donateList: '请我喝咖啡'
  },
  playlistType: {
    title: '歌单分类',
    showAll: '显示全部',
    hide: '隐藏一些'
  },
  recommendAlbum: {
    title: '最新专辑'
  },
  recommendSinger: {
    title: '每日推荐',
    songlist: '每日推荐列表'
  },
  recommendSonglist: {
    title: '本周最热音乐'
  },
  searchBar: {
    login: '登录',
    toLogin: '去登录',
    logout: '退出登录',
    set: '设置',
    theme: '主题',
    restart: '重启',
    refresh: '刷新',
    currentVersion: '当前版本',
    searchPlaceholder: '搜索点什么吧...',
    zoom: '页面缩放',
    zoom100: '标准缩放100%',
    resetZoom: '点击重置缩放',
    zoomDefault: '标准缩放'
  },
  titleBar: {
    closeTitle: '请选择关闭方式',
    minimizeToTray: '最小化到托盘',
    exitApp: '退出应用',
    rememberChoice: '记住我的选择',
    closeApp: '关闭应用'
  },
  userPlayList: {
    title: '{name}的常听'
  },
  musicList: {
    searchSongs: '搜索歌曲',
    noSearchResults: '没有找到相关歌曲',
    switchToNormal: '切换到默认布局',
    switchToCompact: '切换到紧凑布局',
    playAll: '播放全部',
    collect: '收藏',
    collectSuccess: '收藏成功',
    cancelCollectSuccess: '取消收藏成功',
    operationFailed: '操作失败',
    cancelCollect: '取消收藏',
    addToPlaylist: '添加到播放列表',
    addToPlaylistSuccess: '添加到播放列表成功',
    songsAlreadyInPlaylist: '歌曲已存在于播放列表中'
  },
  playlist: {
    import: {
      button: '歌单导入',
      title: '歌单导入',
      description: '支持通过元数据/文字/链接三种方式导入歌单',
      linkTab: '链接导入',
      textTab: '文字导入',
      localTab: '元数据导入',
      linkPlaceholder: '请输入歌单链接，每行一个',
      textPlaceholder: '请输入歌曲信息，格式为：歌曲名 歌手名',
      localPlaceholder: '请输入JSON格式的歌曲元数据',
      linkTips: '支持的链接来源：',
      linkTip1: '将歌单分享到微信/微博/QQ后复制链接',
      linkTip2: '直接复制歌单/个人主页链接',
      linkTip3: '直接复制文章链接',
      textTips: '请输入歌曲信息，每行一首歌',
      textFormat: '格式：歌曲名 歌手名',
      localTips: '请添加歌曲元数据',
      localFormat: '格式示例：',
      songNamePlaceholder: '歌曲名称',
      artistNamePlaceholder: '艺术家名称',
      albumNamePlaceholder: '专辑名称',
      addSongButton: '添加歌曲',
      addLinkButton: '添加链接',
      importToStarPlaylist: '导入到我喜欢的音乐',
      playlistNamePlaceholder: '请输入歌单名称',
      importButton: '开始导入',
      emptyLinkWarning: '请输入歌单链接',
      emptyTextWarning: '请输入歌曲信息',
      emptyLocalWarning: '请输入歌曲元数据',
      invalidJsonFormat: 'JSON格式不正确',
      importSuccess: '导入任务创建成功',
      importFailed: '导入失败',
      importStatus: '导入状态',
      refresh: '刷新',
      taskId: '任务ID',
      status: '状态',
      successCount: '成功数量',
      failReason: '失败原因',
      unknownError: '未知错误',
      statusPending: '等待处理',
      statusProcessing: '处理中',
      statusSuccess: '导入成功',
      statusFailed: '导入失败',
      statusUnknown: '未知状态',
      taskList: '任务列表',
      taskListTitle: '导入任务列表',
      action: '操作',
      select: '选择',
      fetchTaskListFailed: '获取任务列表失败',
      noTasks: '暂无导入任务',
      clearTasks: '清除任务',
      clearTasksConfirmTitle: '确认清除',
      clearTasksConfirmContent: '确定要清除所有导入任务记录吗？此操作不可恢复。',
      confirm: '确认',
      cancel: '取消',
      clearTasksSuccess: '任务列表已清除',
      clearTasksFailed: '清除任务列表失败'
    }
  },
  settings: '设置',
  user: '用户',
  toplist: '排行榜',
  history: '收藏历史',
  list: '歌单',
  mv: 'MV',
  home: '首页',
  search: '搜索'
};
