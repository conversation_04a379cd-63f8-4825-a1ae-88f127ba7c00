export default {
  title: {
    hotSearch: '人気検索リスト',
    searchList: '検索リスト',
    searchHistory: '検索履歴'
  },
  button: {
    clear: 'クリア',
    back: '戻る',
    playAll: 'リストを再生'
  },
  loading: {
    more: '読み込み中...',
    failed: '検索に失敗しました'
  },
  noMore: 'これ以上ありません',
  error: {
    searchFailed: '検索に失敗しました'
  },
  search: {
    single: '楽曲',
    album: 'アルバム',
    playlist: 'プレイリスト',
    mv: 'MV',
    bilibili: 'B<PERSON>bili'
  }
};