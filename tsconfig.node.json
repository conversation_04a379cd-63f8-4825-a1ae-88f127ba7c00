{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": [
    "electron.vite.config.*",
    "src/main/**/*",
    "src/preload/**/*",
    "src/i18n/**/*"
  ],
  "compilerOptions": {
    "composite": true,
    "types": [
      "electron-vite/node"
    ],
    "moduleResolution": "bundler",
  },
  "paths": {
    "@/*": [
      "src/renderer/*"
    ],
    "@renderer/*": [
      "src/renderer/*"
    ],
    "@main/*": [
      "src/main/*"
    ],
    "@i18n/*": [
      "src/i18n/*"
    ]
  }
}