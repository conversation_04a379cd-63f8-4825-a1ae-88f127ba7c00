export default {
  nowPlaying: 'Now Playing',
  playlist: 'Playlist',
  lyrics: 'Lyrics',
  previous: 'Previous',
  play: 'Play',
  pause: 'Pause',
  next: 'Next',
  volumeUp: 'Volume Up',
  volumeDown: 'Volume Down',
  mute: 'Mute',
  unmute: 'Unmute',
  songNum: 'Song Number: {num}',
  addCorrection: 'Add {num} seconds',
  subtractCorrection: 'Subtract {num} seconds',
  playFailed: 'Play Failed, Play Next Song',
  playMode: {
    sequence: 'Sequence',
    loop: 'Loop',
    random: 'Random'
  },
  fullscreen: {
    enter: 'Enter Fullscreen',
    exit: 'Exit Fullscreen'
  },
  close: 'Close',
  modeHint: {
    single: 'Single',
    list: 'Next'
  },
  lrc: {
    noLrc: 'No lyrics, please enjoy'
  },
  reparse: {
    title: 'Select Music Source',
    desc: 'Click a source to directly reparse the current song. This source will be used next time this song plays.',
    success: 'Reparse successful',
    failed: 'Reparse failed',
    warning: 'Please select a music source',
    bilibiliNotSupported: 'Bilibili videos do not support reparsing',
    processing: 'Processing...',
    clear: 'Clear Custom Source'
  },
  playBar: {
    expand: 'Expand Lyrics',
    collapse: 'Collapse Lyrics',
    like: 'Like',
    lyric: 'Lyric',
    noSongPlaying: 'No song playing',
    eq: 'Equalizer',
    playList: 'Play List',
    reparse: 'Reparse',
    playMode: {
      sequence: 'Sequence',
      loop: 'Loop',
      random: 'Random'
    },
    play: 'Play',
    pause: 'Pause',
    prev: 'Previous',
    next: 'Next',
    volume: 'Volume',
    favorite: 'Favorite {name}',
    unFavorite: 'Unfavorite {name}',
    playbackSpeed: 'Playback Speed',
    advancedControls: 'Advanced Controls'
  },
  eq: {
    title: 'Equalizer',
    reset: 'Reset',
    on: 'On',
    off: 'Off',
    bass: 'Bass',
    midrange: 'Midrange',
    treble: 'Treble',
    presets: {
      flat: 'Flat',
      pop: 'Pop',
      rock: 'Rock',
      classical: 'Classical',
      jazz: 'Jazz',
      electronic: 'Electronic',
      hiphop: 'Hip-Hop',
      rb: 'R&B',
      metal: 'Metal',
      vocal: 'Vocal',
      dance: 'Dance',
      acoustic: 'Acoustic',
      custom: 'Custom'
    }
  },
  // Sleep timer related
  sleepTimer: {
    title: 'Sleep Timer',
    cancel: 'Cancel Timer',
    timeMode: 'By Time',
    songsMode: 'By Songs',
    playlistEnd: 'After Playlist',
    afterPlaylist: 'After Playlist Ends',
    activeUntilEnd: 'Active until end of playlist',
    minutes: 'min',
    hours: 'hr',
    songs: 'songs',
    set: 'Set',
    timerSetSuccess: 'Timer set for {minutes} minutes',
    songsSetSuccess: 'Timer set for {songs} songs',
    playlistEndSetSuccess: 'Timer set to end after playlist',
    timerCancelled: 'Sleep timer cancelled',
    timerEnded: 'Sleep timer ended',
    playbackStopped: 'Music playback stopped',
    minutesRemaining: '{minutes} min remaining',
    songsRemaining: '{count} songs remaining'
  },
  playList: {
    clearAll: 'Clear Playlist',
    alreadyEmpty: 'Playlist is already empty',
    cleared: 'Playlist cleared',
    empty: 'Playlist is empty',
    clearConfirmTitle: 'Clear Playlist',
    clearConfirmContent:
      'This will clear all songs in the playlist and stop the current playback. Continue?'
  }
};
