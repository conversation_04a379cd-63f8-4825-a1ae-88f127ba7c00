name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macos-latest, windows-latest, ubuntu-latest]

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm install

      # MacOS Build
      - name: Build MacOS
        if: matrix.os == 'macos-latest'
        run: |
          export ELECTRON_BUILDER_EXTRA_ARGS="--universal"
          npm run build:mac
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CSC_IDENTITY_AUTO_DISCOVERY: false
          DEBUG: electron-builder

      # Windows Build
      - name: Build Windows
        if: matrix.os == 'windows-latest'
        run: npm run build:win
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Linux Build
      - name: Build Linux
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev webkit2gtk-4.0 libappindicator3-dev librsvg2-dev patchelf
          npm run build:linux
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Get version from tag
      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_ENV
        shell: bash

      # Read release notes
      - name: Read release notes
        id: release_notes
        run: |
          NOTES=$(awk "/## \[v${{ env.VERSION }}\]/{p=1;print;next} /## \[v/{p=0}p" CHANGELOG.md)
          echo "NOTES<<EOF" >> $GITHUB_ENV
          echo "$NOTES" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
        shell: bash

      # Upload artifacts
      - name: Upload artifacts
        uses: softprops/action-gh-release@v1
        with:
          files: |
            dist/*.dmg
            dist/*.exe
            dist/*.deb
            dist/*.rpm
            dist/*.AppImage
            dist/latest*.yml
            dist/*.blockmap
          body: ${{ env.NOTES }}
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
