export default {
  profile: {
    followers: 'Followers',
    following: 'Following',
    level: 'Level'
  },
  playlist: {
    created: 'Created Playlists',
    mine: 'Mine',
    trackCount: '{count} tracks',
    playCount: 'Played {count} times'
  },
  ranking: {
    title: 'Listening History',
    playCount: '{count} times'
  },
  follow: {
    title: 'Follow List',
    viewPlaylist: 'View Playlist',
    noFollowings: 'No Followings',
    loadMore: 'Load More',
    noSignature: 'This guy is lazy, nothing left',
    userFollowsTitle: "'s Followings",
    myFollowsTitle: 'My Followings'
  },
  follower: {
    title: 'Follower List',
    noFollowers: 'No Followers',
    loadMore: 'Load More',
    userFollowersTitle: "'s Followers",
    myFollowersTitle: 'My Followers'
  },
  detail: {
    playlists: 'Playlists',
    records: 'Listening History',
    noPlaylists: 'No Playlists',
    noRecords: 'No Listening History',
    artist: 'Artist',
    noSignature: 'This guy is lazy, nothing left',
    invalidUserId: 'Invalid User ID',
    noRecordPermission: "{name} doesn't let you see your listening history"
  },
  message: {
    loadFailed: 'Failed to load user page',
    deleteSuccess: 'Successfully deleted',
    deleteFailed: 'Failed to delete'
  }
};
